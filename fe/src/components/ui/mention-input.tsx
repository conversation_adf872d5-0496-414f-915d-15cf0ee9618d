import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react'
import { User, Crown, Eye, Camera } from 'lucide-react'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'
import { useHubParticipants } from '@/hooks/collaboration-hubs'
import { useTranslations } from '@/lib/i18n/typed-translations'
import { useCurrentUser } from '@/contexts/auth-context'
import type { HubParticipantResponse } from '@/components/collaboration-hub/types'

// Constants for better maintainability
const MENTION_INSERTION_DELAY = 50 // ms to wait before allowing new mention detection after insertion
const PARTICIPANTS_FETCH_SIZE = 100 // Number of participants to fetch
const MENTION_CHAR_REGEX = /^[a-zA-Z0-9._\s-]*$/ // Valid characters in mention text
const MENTION_VALID_CHARS = /[a-zA-Z0-9._\s-]/ // Valid mention characters for typing detection

// Enhanced participant type with disambiguation info for duplicate names
type EnhancedParticipant = HubParticipantResponse & {
  hasDuplicateName?: boolean
  disambiguationInfo?: string
}

interface MentionInputProps {
  hubId: number
  value: string // Email-based value from parent (source of truth)
  onChange: (value: string) => void // Always sends email-based value to parent
  onKeyDown?: (e: React.KeyboardEvent) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  children: (props: {
    ref: React.RefObject<HTMLInputElement | HTMLTextAreaElement>
    value: string // Display value (name-based, for UI)
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
    onKeyDown: (e: React.KeyboardEvent) => void
    onSelect: (e: React.SyntheticEvent) => void
    placeholder?: string
    disabled?: boolean
    className?: string
  }) => React.ReactNode
}

interface MentionMatch {
  start: number
  end: number
  query: string
}

const MentionInputComponent = function MentionInput({
                               hubId,
                               value,
                               onChange,
                               onKeyDown,
                               placeholder,
                               disabled,
                               className,
                               children
                             }: MentionInputProps) {
  const { t, keys } = useTranslations()
  const currentUser = useCurrentUser()
  const [isOpen, setIsOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [mentionMatch, setMentionMatch] = useState<MentionMatch | null>(null)
  const [displayValue, setDisplayValue] = useState<string>('') // What user sees (@Name format)
  const [justInsertedMention, setJustInsertedMention] = useState(false) // Track recent mention insertion
  const [lastProcessedValue, setLastProcessedValue] = useState<string>('') // Track last processed parent value
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null)
  const popoverRef = useRef<HTMLDivElement>(null)

  const { data: participantsData, isLoading } = useHubParticipants(hubId, {
    enabled: !!hubId && !disabled,
    size: PARTICIPANTS_FETCH_SIZE
  })

  // Helper function to escape special regex characters - memoized to prevent recreation
  const escapeRegex = useCallback((string: string): string => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }, [])

  // Convert display value (@Name) to email format (@email) for backend
  // CRITICAL: Only convert display names that are NOT already in email format
  const convertToEmailFormat = useCallback((text: string): string => {
    if (!participantsData?.content?.length) return text

    let result = text
    const replacedRanges: Array<{start: number, end: number}> = []

    // ARCHITECTURE: Robust Email Format Conversion
    // 1. First, identify all existing email mentions to preserve them
    // 2. Then, only convert display names that are not already email format
    // 3. This prevents corruption of existing email mentions

    // Step 1: Find and protect existing email mentions
    // CRITICAL FIX: Robust email regex that properly handles @ symbols within email addresses
    // Pattern: @(<EMAIL>) - matches complete email addresses
    // [^\s@] for username part, then @, then [^\s] for domain part
    const emailMentionPattern = /@([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(?=\s|$)/gi
    const existingEmailMentions: Array<{start: number, end: number}> = []
    let emailMatch

    while ((emailMatch = emailMentionPattern.exec(result)) !== null) {
      existingEmailMentions.push({
        start: emailMatch.index,
        end: emailMatch.index + emailMatch[0].length
      })
    }

    // Step 2: Sort participants by name length (longest first) to avoid partial matches
    const sortedParticipants = [...participantsData.content]
      .filter(p => p.name && p.email)
      .sort((a, b) => (b.name?.length || 0) - (a.name?.length || 0))

    sortedParticipants.forEach(participant => {
      if (participant.name && participant.email) {
        // Find all matches for this participant's name
        const namePattern = new RegExp(`@${escapeRegex(participant.name)}(?=\\s|$|[^a-zA-Z0-9@])`, 'gi')
        let match
        const matches = []

        // Reset regex lastIndex to ensure we find all matches
        namePattern.lastIndex = 0

        while ((match = namePattern.exec(result)) !== null) {
          const matchStart = match.index
          const matchEnd = match.index + match[0].length

          // CRITICAL: Skip if this overlaps with an existing email mention
          const overlapsWithEmail = existingEmailMentions.some(emailRange =>
            (matchStart >= emailRange.start && matchStart < emailRange.end) ||
            (matchEnd > emailRange.start && matchEnd <= emailRange.end) ||
            (matchStart <= emailRange.start && matchEnd >= emailRange.end)
          )

          if (overlapsWithEmail) {
            continue // Skip this match as it's part of an existing email mention
          }

          // Check if this match overlaps with any already-replaced range
          const overlaps = replacedRanges.some(range =>
            (matchStart >= range.start && matchStart < range.end) ||
            (matchEnd > range.start && matchEnd <= range.end) ||
            (matchStart <= range.start && matchEnd >= range.end)
          )

          if (!overlaps) {
            matches.push({
              match: match[0],
              start: matchStart,
              end: matchEnd,
              replacement: `@${participant.email}`
            })
          }
        }

        // Apply replacements in reverse order to maintain correct positions
        matches.reverse().forEach(matchInfo => {
          result = result.substring(0, matchInfo.start) +
                   matchInfo.replacement +
                   result.substring(matchInfo.end)

          // Track the replaced range (adjust for length difference)
          const lengthDiff = matchInfo.replacement.length - matchInfo.match.length
          replacedRanges.push({
            start: matchInfo.start,
            end: matchInfo.start + matchInfo.replacement.length
          })

          // Adjust existing ranges that come after this replacement
          replacedRanges.forEach(range => {
            if (range.start > matchInfo.start) {
              range.start += lengthDiff
              range.end += lengthDiff
            }
          })
        })
      }
    })

    return result
  }, [participantsData?.content, escapeRegex])

  // Convert email format (@email) to display format (@Name) for UI
  const convertToDisplayFormat = useCallback((text: string): string => {
    if (!participantsData?.content?.length) return text

    let result = text

    // ARCHITECTURE: Robust Email-to-Name Conversion
    // Create a map for efficient lookup and handle edge cases
    const emailToParticipant = new Map<string, HubParticipantResponse>()
    participantsData.content.forEach(participant => {
      if (participant.email) {
        emailToParticipant.set(participant.email.toLowerCase(), participant)
      }
    })

    // Find all email mentions in the text
    // CRITICAL FIX: Robust email regex that properly handles @ symbols within email addresses
    const emailMentionPattern = /@([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(?=\s|$)/gi
    let match
    const replacements: Array<{start: number, end: number, replacement: string}> = []

    while ((match = emailMentionPattern.exec(result)) !== null) {
      const email = match[1].toLowerCase()
      const participant = emailToParticipant.get(email)

      if (participant) {
        // Use participant's display name if available
        const displayName = participant.name || participant.email?.split('@')[0] || 'Unknown'
        replacements.push({
          start: match.index,
          end: match.index + match[0].length,
          replacement: `@${displayName}`
        })
      } else {
        // Handle missing participant - show email prefix as fallback
        const emailPrefix = match[1].split('@')[0]
        replacements.push({
          start: match.index,
          end: match.index + match[0].length,
          replacement: `@${emailPrefix}`
        })
      }
    }

    // Apply replacements in reverse order to maintain correct positions
    replacements.reverse().forEach(replacement => {
      result = result.substring(0, replacement.start) +
               replacement.replacement +
               result.substring(replacement.end)
    })

    return result
  }, [participantsData?.content, escapeRegex])

  const filteredParticipants = useMemo((): EnhancedParticipant[] => {
    if (!mentionMatch) return []

    const participants = participantsData?.content || []

    // Filter out current user and apply search query
    const filtered = participants.filter(p => {
      // Exclude current user from suggestions
      // Handle both internal users (with user.id) and external participants (email-only)

      // Primary check: Compare by email (works for both internal and external users)
      if (currentUser?.email && p.email) {
        const currentUserEmail = currentUser.email.toLowerCase().trim()
        const participantEmail = p.email.toLowerCase().trim()
        if (currentUserEmail === participantEmail) {
          return false
        }
      }

      // Secondary check: For internal users, also compare by ID as fallback
      // This handles edge cases where email might not match but ID does
      if (currentUser?.id && p.id && typeof currentUser.id === 'number' && typeof p.id === 'number') {
        if (currentUser.id === p.id) {
          return false
        }
      }

      const query = mentionMatch.query.toLowerCase()
      const emailPrefix = p.email?.split('@')[0]?.toLowerCase() || ''
      const name = p.name?.toLowerCase() || ''

      // Prioritize exact email prefix matches
      return emailPrefix.includes(query) || name.includes(query)
    }).sort((a, b) => {
      const query = mentionMatch.query.toLowerCase()
      const aEmailPrefix = a.email?.split('@')[0]?.toLowerCase() || ''
      const bEmailPrefix = b.email?.split('@')[0]?.toLowerCase() || ''

      // Prioritize exact email prefix matches first
      const aEmailMatch = aEmailPrefix.startsWith(query)
      const bEmailMatch = bEmailPrefix.startsWith(query)

      if (aEmailMatch && !bEmailMatch) return -1
      if (!aEmailMatch && bEmailMatch) return 1

      // Then prioritize email prefix contains over name matches
      const aEmailContains = aEmailPrefix.includes(query)
      const bEmailContains = bEmailPrefix.includes(query)

      if (aEmailContains && !bEmailContains) return -1
      if (!aEmailContains && bEmailContains) return 1

      return 0
    })

    // ARCHITECTURE: Duplicate Name Handling
    // Detect participants with duplicate display names and add disambiguation info
    const nameCount = new Map<string, number>()
    filtered.forEach(p => {
      const name = p.name || 'Unknown'
      nameCount.set(name, (nameCount.get(name) || 0) + 1)
    })

    // Add disambiguation info for participants with duplicate names
    return filtered.map(p => ({
      ...p,
      hasDuplicateName: (nameCount.get(p.name || 'Unknown') || 0) > 1,
      disambiguationInfo: (nameCount.get(p.name || 'Unknown') || 0) > 1
        ? p.email?.split('@')[0] || p.email || 'Unknown'
        : undefined
    }))
  }, [mentionMatch, participantsData?.content, currentUser?.email, currentUser?.id])

  // Manage popper visibility based on mention match and available participants
  useEffect(() => {
    // Only show popper if there's a mention match AND there are participants to show
    // This prevents showing "No participants found" message
    const shouldShowPopper = !!mentionMatch && filteredParticipants.length > 0
    setIsOpen(shouldShowPopper)
  }, [mentionMatch, filteredParticipants.length])

  // Handle click outside to close popup
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isOpen &&
          popoverRef.current &&
          !popoverRef.current.contains(event.target as Node) &&
          inputRef.current &&
          !inputRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setMentionMatch(null)
        setSelectedIndex(0)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  const getInitials = (name?: string) => {
    if (!name) return '?'
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Crown className="h-3 w-3 text-yellow-600" />
      case 'reviewer':
        return <Eye className="h-3 w-3 text-blue-600" />
      case 'reviewer_creator':
        return <Camera className="h-3 w-3 text-purple-600" />
      case 'content_creator':
        return <User className="h-3 w-3 text-green-600" />
      default:
        return <User className="h-3 w-3 text-gray-600" />
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return t(keys.collaborationHubs.roles.admin)
      case 'reviewer':
        return t(keys.collaborationHubs.roles.reviewer)
      case 'reviewer_creator':
        return t(keys.collaborationHubs.roles.reviewer_creator)
      case 'content_creator':
        return t(keys.collaborationHubs.roles.content_creator)
      default:
        return role
    }
  }

  const findMentionMatch = useCallback((text: string, cursor: number): MentionMatch | null => {
    const before = text.slice(0, cursor)

    // CRITICAL FIX: Smart @ symbol detection that distinguishes between
    // @ symbols that start mentions vs @ symbols within email addresses

    // Find all @ symbols before cursor
    const atSymbols: number[] = []
    for (let i = 0; i < before.length; i++) {
      if (before[i] === '@') {
        atSymbols.push(i)
      }
    }

    if (atSymbols.length === 0) return null

    // Check each @ symbol from right to left to find the one that starts a new mention
    for (let i = atSymbols.length - 1; i >= 0; i--) {
      const atIndex = atSymbols[i]
      const afterAt = before.slice(atIndex + 1)

      // Skip if this @ is likely part of an email address
      // Check if there's another @ symbol after this one (indicating email <NAME_EMAIL>)
      const hasSubsequentAt = atSymbols.some(otherAt => otherAt > atIndex)
      if (hasSubsequentAt) {
        // This @ might be part of an email address, check if it looks like a complete email
        const textAfterAt = text.slice(atIndex + 1)
        const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/
        if (emailPattern.test(textAfterAt)) {
          continue // Skip this @, it's part of an email address
        }
      }

      // Only exclude mentions that end with a space (user pressed space after completing a mention)
      if (afterAt.endsWith(' ')) {
        continue
      }

      // Match valid mention characters (letters, numbers, dots, underscores, spaces for multi-word names)
      if (MENTION_CHAR_REGEX.test(afterAt)) {
        return {
          start: atIndex,
          end: cursor,
          query: afterAt
        }
      }
    }

    return null
  }, [])





  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newDisplayValue = e.target.value
    const cursor = e.target.selectionStart ?? 0

    // Update display value
    setDisplayValue(newDisplayValue)

    // CRITICAL FIX: Only convert to email format when not in mention insertion mode
    // This prevents corruption of correctly inserted emails during typing
    if (!justInsertedMention) {
      const emailFormatValue = convertToEmailFormat(newDisplayValue)
      onChange(emailFormatValue)
    }

    // Enhanced mention completion detection
    const wasTyping = newDisplayValue.length > displayValue.length
    const typedChar = wasTyping ? newDisplayValue[cursor - 1] : ''

    // If user typed a space and dropdown is open, close it (completed mention)
    if (wasTyping && typedChar === ' ' && isOpen) {
      setIsOpen(false)
      setMentionMatch(null)
      setSelectedIndex(0)
      return
    }

    // If user typed a non-mention character after what looks like a completed mention, close dropdown
    if (wasTyping && isOpen && mentionMatch) {
      const beforeCursor = newDisplayValue.slice(0, cursor)
      const afterLastAt = beforeCursor.slice(beforeCursor.lastIndexOf('@') + 1)

      // If there's a space in the mention text and user is typing after it, close dropdown
      if (afterLastAt.includes(' ') && !typedChar.match(MENTION_VALID_CHARS)) {
        setIsOpen(false)
        setMentionMatch(null)
        setSelectedIndex(0)
        return
      }
    }

    // Find mention match for dropdown - always detect mentions, even after recent insertion
    const match = findMentionMatch(newDisplayValue, cursor)

    // If we just inserted a mention, don't immediately reopen dropdown for the same position
    // but still allow detection of new @ symbols at different positions
    if (justInsertedMention && match && mentionMatch &&
        match.start === mentionMatch.start && match.end === mentionMatch.end) {
      return
    }

    setMentionMatch(match)
    // Note: isOpen will be managed by the effect that considers both match and filtered participants
    setSelectedIndex(0)
  }, [onChange, findMentionMatch, displayValue, isOpen, convertToEmailFormat, mentionMatch, justInsertedMention])

  const insertMention = useCallback((participant: HubParticipantResponse) => {
    if (!mentionMatch || !inputRef.current) return

    // ARCHITECTURE: Direct Email Insertion with Correct Participant Data
    // CRITICAL FIX: Use the selected participant's email directly instead of convertToEmailFormat
    // This prevents wrong email insertion when multiple participants have the same display name

    // Work with the display value for UI positioning (mentionMatch positions are based on display value)
    const beforeMentionDisplay = displayValue.slice(0, mentionMatch.start)
    const afterMentionDisplay = displayValue.slice(mentionMatch.end)

    // Create the new display value with the mention
    const participantName = participant.name || participant.email?.split('@')[0] || 'Unknown'
    const displayMentionText = `@${participantName}`
    const newDisplayValue = beforeMentionDisplay + displayMentionText + ' ' + afterMentionDisplay

    // CRITICAL FIX: Build email format value manually using the selected participant's email
    // Convert existing display mentions to email format, but use the selected participant's email for the new mention
    const beforeMentionEmail = convertToEmailFormat(beforeMentionDisplay)
    const afterMentionEmail = convertToEmailFormat(afterMentionDisplay)
    const selectedParticipantEmail = `@${participant.email}`
    const emailFormatValue = beforeMentionEmail + selectedParticipantEmail + ' ' + afterMentionEmail

    console.log(participant)
    console.log(newDisplayValue)
    console.log(emailFormatValue)

    // Update display value for UI
    setDisplayValue(newDisplayValue)

    // Send email format as source of truth to parent component
    onChange(emailFormatValue)

    // Track the email format value we just sent to prevent useEffect from overwriting display value
    setLastProcessedValue(emailFormatValue)

    // Close dropdown and mark that we just inserted a mention
    setIsOpen(false)
    setMentionMatch(null)
    setSelectedIndex(0)
    setJustInsertedMention(true)

    // Position cursor after the mention
    const newCursor = mentionMatch.start + displayMentionText.length + 1 // +1 for space

    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.setSelectionRange(newCursor, newCursor)
        inputRef.current.focus()
      }
      // Clear the flag after a short delay to allow normal mention detection to resume
      setTimeout(() => setJustInsertedMention(false), MENTION_INSERTION_DELAY)
    }, 0)
  }, [mentionMatch, displayValue, onChange, convertToEmailFormat])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (isOpen && filteredParticipants.length > 0) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          setSelectedIndex(prev => (prev + 1) % filteredParticipants.length)
          break
        case 'ArrowUp':
          e.preventDefault()
          setSelectedIndex(prev => (prev - 1 + filteredParticipants.length) % filteredParticipants.length)
          break
        case 'Enter':
        case 'Tab':
          e.preventDefault()
          insertMention(filteredParticipants[selectedIndex])
          break
        case 'Escape':
          e.preventDefault()
          setIsOpen(false)
          setMentionMatch(null)
          setSelectedIndex(0)
          break
        case ' ':
          // Space key - close the popup if we're at the end of a mention
          if (mentionMatch) {
            setIsOpen(false)
            setMentionMatch(null)
            setSelectedIndex(0)
          }
          onKeyDown?.(e)
          break
        default:
          onKeyDown?.(e)
      }
    } else {
      onKeyDown?.(e)
    }
  }, [isOpen, filteredParticipants, selectedIndex, mentionMatch, onKeyDown, insertMention])


  const handleParticipantSelect = useCallback((e: React.MouseEvent, p: HubParticipantResponse) => {
    e.preventDefault()
    e.stopPropagation()
    insertMention(p)
  }, [insertMention])

  const handleSelect = useCallback(() => {
    // Don't interfere with mention selection when popup is open
    if (isOpen) {
      return
    }

    // Update mention match based on cursor position
    setTimeout(() => {
      const cursor = inputRef.current?.selectionStart ?? 0
      const match = findMentionMatch(displayValue, cursor)

      if (match) {
        setMentionMatch(match)
        // isOpen will be managed by the effect based on match and filtered participants
        setSelectedIndex(0)
      } else if (mentionMatch) {
        setMentionMatch(null)
        // isOpen will be managed by the effect
        setSelectedIndex(0)
      }
    }, 0)
  }, [displayValue, findMentionMatch, mentionMatch, isOpen])

  // Initialize display value from email-based value when it changes
  useEffect(() => {
    // CRITICAL FIX: Always sync from parent value when it changes
    // This ensures display value stays in sync with the correct email format from parent
    if (value !== lastProcessedValue) {
      const newDisplayValue = convertToDisplayFormat(value)
      setDisplayValue(newDisplayValue)
      setLastProcessedValue(value)
    }
  }, [value, convertToDisplayFormat, lastProcessedValue])

  return (
    <div className={cn("relative", className)}>
      {children({
        ref: inputRef,
        value: displayValue, // Display value (name-based, for UI)
        onChange: handleInputChange,
        onKeyDown: handleKeyDown,
        onSelect: handleSelect,
        placeholder,
        disabled,
        className
      })}



      {isOpen && (
        <div
          ref={popoverRef}
          className="absolute z-50 w-80 bottom-full mb-1 bg-popover border border-border rounded-md shadow-md"
        >
          <div className="p-2">
            {isLoading ? (
              <div className="text-center py-4 text-sm text-muted-foreground">
                {t(keys.ui.mentionInput.loadingParticipants)}
              </div>
            ) : (
              <ScrollArea className="max-h-64">
                {filteredParticipants.map((p, index) => (
                  <div
                    key={p.id}
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      handleParticipantSelect(e, p)
                    }}
                    className={cn(
                      "flex items-center gap-3 p-3 cursor-pointer rounded-md hover:bg-accent",
                      index === selectedIndex && "bg-accent"
                    )}
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="text-xs">
                        {getInitials(p.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="font-medium text-sm truncate">
                          {p.name || 'Unknown'}
                          {p.hasDuplicateName && p.disambiguationInfo && (
                            <span className="text-muted-foreground font-normal ml-1">
                              ({p.disambiguationInfo})
                            </span>
                          )}
                        </p>
                        {p.role && <span>{getRoleIcon(p.role)}</span>}
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <p className="text-xs text-muted-foreground truncate">
                          {p.email}
                        </p>
                        {p.role && (
                          <Badge variant="secondary" className="text-xs px-1 py-0">
                            {getRoleLabel(p.role)}
                          </Badge>
                        )}
                        {p.isExternal && (
                          <Badge variant="outline" className="text-xs px-1 py-0">
                            External
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </ScrollArea>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

// Export memoized component with proper comparison
export const MentionInput = React.memo(MentionInputComponent, (prevProps, nextProps) => {
  // Custom comparison function to prevent unnecessary re-renders
  return (
    prevProps.hubId === nextProps.hubId &&
    prevProps.value === nextProps.value &&
    prevProps.placeholder === nextProps.placeholder &&
    prevProps.disabled === nextProps.disabled &&
    prevProps.className === nextProps.className &&
    prevProps.onChange === nextProps.onChange &&
    prevProps.onKeyDown === nextProps.onKeyDown
  )
})
